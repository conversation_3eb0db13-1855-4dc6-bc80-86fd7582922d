#!/usr/bin/env python3
"""
Test script to verify that JSON responses include UTF-8 charset header.
This script tests various endpoints to ensure Korean characters are properly handled.
"""

import requests
import json

def test_utf8_charset():
    """Test that JSON responses include UTF-8 charset"""
    
    # Base URL for the Flask application
    base_url = "http://localhost:7000"
    
    # Test endpoints that return JSON with Korean text
    test_endpoints = [
        "/",  # Root endpoint
        "/api/language-groups",  # Language groups
        "/api/session-status",  # Session status
    ]
    
    print("🧪 Testing UTF-8 charset in JSON responses")
    print("=" * 50)
    
    for endpoint in test_endpoints:
        try:
            url = f"{base_url}{endpoint}"
            print(f"\n📡 Testing: {url}")
            
            response = requests.get(url, timeout=10)
            
            # Check status code
            print(f"   Status: {response.status_code}")
            
            # Check content type
            content_type = response.headers.get('Content-Type', '')
            print(f"   Content-Type: {content_type}")
            
            # Check if UTF-8 charset is included
            if 'application/json' in content_type:
                if 'charset=utf-8' in content_type.lower():
                    print("   ✅ UTF-8 charset is properly set")
                else:
                    print("   ❌ UTF-8 charset is missing")
                    print(f"      Expected: application/json; charset=utf-8")
                    print(f"      Actual: {content_type}")
            else:
                print(f"   ⚠️  Not a JSON response: {content_type}")
            
            # Try to decode JSON and check for Korean characters
            try:
                data = response.json()
                json_str = json.dumps(data, ensure_ascii=False)
                
                # Check if there are Korean characters
                korean_chars = any('\uac00' <= char <= '\ud7af' for char in json_str)
                if korean_chars:
                    print("   🇰🇷 Contains Korean characters")
                else:
                    print("   📝 No Korean characters detected")
                    
            except json.JSONDecodeError:
                print("   ❌ Failed to decode JSON response")
                
        except requests.exceptions.RequestException as e:
            print(f"   ❌ Request failed: {e}")
        except Exception as e:
            print(f"   ❌ Unexpected error: {e}")
    
    print("\n" + "=" * 50)
    print("🏁 Test completed")

def test_korean_text_endpoint():
    """Test an endpoint that specifically returns Korean text"""
    
    base_url = "http://localhost:7000"
    
    # Test data with Korean characters
    test_data = {
        "message": "안녕하세요",
        "description": "한국어 텍스트 테스트",
        "status": "성공"
    }
    
    print("\n🇰🇷 Testing Korean text handling")
    print("=" * 50)
    
    try:
        # This would be a test endpoint that echoes back Korean text
        # For now, we'll just test the root endpoint
        url = f"{base_url}/"
        response = requests.get(url, timeout=10)
        
        content_type = response.headers.get('Content-Type', '')
        print(f"Content-Type: {content_type}")
        
        if 'charset=utf-8' in content_type.lower():
            print("✅ UTF-8 charset is properly configured")
        else:
            print("❌ UTF-8 charset configuration issue")
            
    except Exception as e:
        print(f"❌ Test failed: {e}")

if __name__ == "__main__":
    print("🔧 UTF-8 Charset Test for Flask Application")
    print("This script tests that JSON responses include proper UTF-8 charset headers")
    print("to ensure Korean characters are displayed correctly in browsers.\n")
    
    test_utf8_charset()
    test_korean_text_endpoint()
    
    print("\n💡 Tips:")
    print("- Make sure the Flask application is running on http://localhost:7000")
    print("- All JSON responses should include 'charset=utf-8' in Content-Type header")
    print("- This ensures proper rendering of Korean characters in web browsers")
