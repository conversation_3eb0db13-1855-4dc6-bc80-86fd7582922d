# app/blueprints/api/routes.py
import json
import os

from flask import jsonify, request, url_for

from ...services.json_session_service import (get_session_value,
                                              set_session_value)
from ...services.nlp_service import (WordCloudGenerator, analyze_frequency, get_file_path, read_file)
from ...services.session_service import get_or_create_session_id
from . import freq_bp


# Configure CORS for React frontend with HTTPS support
@freq_bp.after_request
def after_request(response):
    # Get CORS allowed origins from environment variable or use a default
    cors_allowed_origins = os.getenv("CORS_ALLOWED_ORIGINS", "*")

    # If specific origins are provided, use them
    if cors_allowed_origins != "*":
        # Check if the request origin is in the allowed origins
        request_origin = request.headers.get("Origin")
        if request_origin:
            if "," in cors_allowed_origins:
                allowed_origins = cors_allowed_origins.split(",")
                if request_origin in allowed_origins:
                    response.headers.add("Access-Control-Allow-Origin", request_origin)
            else:
                if request_origin == cors_allowed_origins:
                    response.headers.add("Access-Control-Allow-Origin", request_origin)
    else:
        # Wildcard for development
        response.headers.add("Access-Control-Allow-Origin", "*")

    response.headers.add(
        "Access-Control-Allow-Headers", "Content-Type,Authorization,X-Requested-With"
    )
    response.headers.add("Access-Control-Allow-Methods", "GET,PUT,POST,DELETE,OPTIONS")
    response.headers.add("Access-Control-Allow-Credentials", "true")

    # Add Vary header to ensure proper caching with CORS
    response.headers.add("Vary", "Origin")

    # Set UTF-8 charset for JSON responses to properly handle Korean characters
    if response.content_type and response.content_type.startswith('application/json'):
        response.content_type = 'application/json; charset=utf-8'

    return response




@freq_bp.route("/word_data", methods=["POST", "OPTIONS"])
def api_get_word_data():
    """단어 목록 데이터만 반환하는 API (수동 선택 모드용)"""
    if request.method == "OPTIONS":
        return "", 200

    try:
        # 파일 정보 가져오기
        session_id = request.args.get("session_id")
        if not session_id:
            session_id = get_or_create_session_id()

        filename = get_session_value(session_id, "uploaded_file")

        column_name = request.form.get("column_name")

        if not session_id or not filename:
            return jsonify({"error": "세션 ID와 파일명이 필요합니다."}), 400

        if not column_name:
            options = get_session_value(session_id, "options")
            column_name = options.get("column_name") if options else None
            if not column_name:
                return jsonify({"error": "세션 ID와 파일명이 필요합니다."}), 400

        # 파일 경로 생성
        file_path = get_file_path(session_id, filename)

        # 파일 존재 확인
        if not os.path.exists(file_path):
            return jsonify({"error": "파일을 찾을 수 없습니다."}), 404

        # 분석 실행 - 수동 선택 모드로 실행하여 단어 데이터만 반환
        result = analyze_frequency(file_path, column_name, selection_type="manual", session_id=session_id)

        if "error" in result:
            return jsonify(result), 400

        # Add full download URLs to the result
        if "output_file" in result:
            result["download_url"] = url_for(
                "process.serve_file",
                filepath=result["output_file"],
                download="true",
                session_id=session_id,
                _external=True,
            )

        if "wordcloud_file" in result and result["wordcloud_file"]:
            result["wordcloud_url"] = url_for(
                "process.serve_file",
                filepath=result["wordcloud_file"],
                download="false",
                session_id=session_id,
                _external=True,
            )

        return jsonify(result)
    except Exception as e:
        # logger.error(f"단어 데이터 처리 오류: {traceback.format_exc()}")
        return jsonify({"error": str(e)}), 500


@freq_bp.route("/analyze", methods=["POST", "OPTIONS"])
def api_analyze():
    """파일 분석 API"""
    if request.method == "OPTIONS":
        return "", 200

    try:
        # 파일 정보 가져오기
        session_id = request.args.get("session_id")
        if not session_id:
            session_id = get_or_create_session_id()

        filename = get_session_value(session_id, "uploaded_file")

        column_name = request.form.get("column_name")

        if not session_id or not filename:
            return jsonify({"error": "세션 ID와 파일명이 필요합니다."}), 400

        if not column_name:
            options = get_session_value(session_id, "options")
            column_name = options.get("column_name") if options else None
            if not column_name:
                return jsonify({"error": "세션 ID와 파일명이 필요합니다."}), 400

        # 추가 파라미터
        selection_type = request.form.get("selection_type", "top_n")
        max_words = request.form.get("max_words", "50")
        cloud_shape = request.form.get("cloud_shape", "rectangle")
        cloud_color = request.form.get("cloud_color", "viridis")
        selected_words = request.form.get("selected_words", None)

        # 파일 경로 생성
        file_path = get_file_path(session_id, filename)

        # 파일 존재 확인
        if not os.path.exists(file_path):
            return jsonify({"error": "파일을 찾을 수 없습니다."}), 404

        # 분석 실행
        result = analyze_frequency(
            file_path,
            column_name,
            selection_type,
            max_words,
            cloud_shape,
            cloud_color,
            selected_words,
            session_id=session_id,
        )

        if "error" in result:
            return jsonify(result), 400

        # Add full download URLs to the result
        if "output_file" in result:
            result["download_url"] = url_for(
                "process.serve_file",
                filepath=result["output_file"],
                download="true",
                session_id=session_id,
                _external=True,
            )

        if "wordcloud_file" in result and result["wordcloud_file"]:
            result["wordcloud_url"] = url_for(
                "process.serve_file",
                filepath=result["wordcloud_file"],
                download="false",
                session_id=session_id,
                _external=True,
            )

        return jsonify(result)
    except Exception as e:
        # logger.error(f"분석 처리 오류: {traceback.format_exc()}")
        return jsonify({"error": str(e)}), 500


@freq_bp.route("/wordcloud", methods=["POST", "OPTIONS"])
def api_generate_wordcloud():
    """선택한 단어로 워드클라우드 생성 API"""
    if request.method == "OPTIONS":
        return "", 200

    try:
        selected_words = request.form.get("selected_words", "[]")
        cloud_shape = request.form.get("cloud_shape", "rectangle")
        cloud_color = request.form.get("cloud_color", "viridis")

        word_list = json.loads(selected_words)

        # 워드클라우드 생성
        session_id = request.args.get("session_id")
        if not session_id:
            session_id = get_or_create_session_id()

        wordcloud_generator = WordCloudGenerator(analysis_type="word_frequency")
        result = wordcloud_generator.generate_from_words(
            word_list, session_id, cloud_shape, cloud_color
        )
        # result = create_wordcloud_from_words(word_list, cloud_shape, cloud_color)

        if "error" in result:
            return jsonify(result), 400

        # Add full download URL to the result
        if 'file_path' in result:
            result["download_url"] = url_for(
                "process.serve_file",
                filepath=result["file_path"],
                download="true",
                session_id=session_id,
                _external=True,
            )
        return jsonify(
            {
                "success": True,
                "session_id": session_id,
                ** result
            }
        )
    except Exception as e:
        # logger.error(f"워드클라우드 생성 오류: {traceback.format_exc()}")
        return jsonify({"error": str(e)}), 500


@freq_bp.route("/edit_words", methods=["POST", "OPTIONS"])
def api_edit_words():
    """단어 편집 후 워드클라우드 재생성 API"""
    if request.method == "OPTIONS":
        return "", 200

    try:
        # Get session info
        session_id = request.args.get("session_id")
        if not session_id:
            session_id = get_or_create_session_id()

        # Get parameters
        edited_words = request.form.get("edited_words", "[]")
        selected_words = request.form.get("selected_words")  # This is now optional
        cloud_shape = request.form.get("cloud_shape", "rectangle")
        cloud_color = request.form.get("cloud_color", "viridis")
        column_name = request.form.get("column_name")

        if not column_name:
            options = get_session_value(session_id, "options")
            column_name = options.get("column_name") if options else None
            if not column_name:
                return jsonify({"error": "Column name is required"}), 400

        try:
            # Parse the edited words JSON
            edited_words_list = json.loads(edited_words)
            if not isinstance(edited_words_list, list):
                return jsonify({"error": "edited_words must be a JSON array"}), 400

            # Get file info
            filename = get_session_value(session_id, "uploaded_file")
            if not filename:
                return jsonify({"error": "No file found in session"}), 400

            # Get file path
            file_path = get_file_path(session_id, filename)

            # Check if file exists
            if not os.path.exists(file_path):
                return jsonify({"error": "File not found"}), 404

            # Create a mapping of original to edited words
            word_mapping = {
                item["original"].lower(): item["new"].lower()
                for item in edited_words_list
            }
            print(word_mapping)
            print(edited_words_list)
            # Apply the edits to the original word list

            data = read_file(file_path)

            def replace_words(text):
                for old, new in word_mapping.items():
                    text = text.lower().replace(old.lower(), new.lower())
                return text

            data[column_name] = data[column_name].apply(replace_words)
            edited_file_name = f"{filename.split('.')[0]}_edited.csv"
            set_session_value(session_id, "edited_file", edited_file_name)
            edited_file_path = get_file_path(session_id, edited_file_name)
            data.to_csv(edited_file_path, index=False)
            # Call analyze with the updated words
            result = analyze_frequency(
                edited_file_path,
                column_name,
                max_words=str(data.shape[0]),  # Use the same number of words as input
                cloud_shape=cloud_shape,
                cloud_color=cloud_color,
                selected_words=selected_words,  # Pass our edited words
                session_id=session_id,
            )

            if "error" in result:
                return jsonify(result), 400

            # Add full download URLs to the result
            if "output_file" in result:
                result["download_url"] = url_for(
                    "process.serve_file",
                    filepath=result["output_file"],
                    download="true",
                    session_id=session_id,
                    _external=True,
                )

            if "wordcloud_file" in result and result["wordcloud_file"]:
                result["wordcloud_url"] = url_for(
                    "process.serve_file",
                    filepath=result["wordcloud_file"],
                    download="false",
                    session_id=session_id,
                    _external=True,
                )

            return jsonify(result)

        except json.JSONDecodeError:
            return jsonify({"error": "Invalid JSON format in edited_words"}), 400

    except Exception as e:
        return jsonify({"error": str(e)}), 500


def cleanup_temp_files():
    """일정 시간이 지난 임시 파일들을 정리하는 함수"""
    try:
        # 이 함수는 필요에 따라 구현 (예: 특정 시간 이전에 생성된 파일 삭제)
        pass
    except Exception as e:
        return jsonify({"error": str(e)}), 500
        # logger.error(f"임시 파일 정리 중 오류 발생: {e}")
