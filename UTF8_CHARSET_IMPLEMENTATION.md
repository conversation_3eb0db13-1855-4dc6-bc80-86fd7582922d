# UTF-8 Charset Implementation for JSON Responses

This document explains the implementation of UTF-8 charset headers for all JSON responses to fix Korean character corruption issues.

## Problem

Korean characters were getting corrupted in JSON responses because the browser was not properly interpreting the character encoding. While <PERSON>lask's `jsonify()` function returns JSON with UTF-8 encoding, the `Content-Type` header was missing the `charset=utf-8` parameter, causing browsers to potentially misinterpret the encoding.

## Solution

Added UTF-8 charset specification to all JSON responses by implementing `after_request` handlers that automatically set the proper `Content-Type` header.

## Implementation Details

### 1. Global Handler (Main Application)

**File**: `backend/app/__init__.py`

Added a global `after_request` handler that applies to all responses:

```python
@app.after_request
def after_request(response):
    """Set UTF-8 charset for JSON responses to properly handle Korean characters"""
    if response.content_type and response.content_type.startswith('application/json'):
        response.content_type = 'application/json; charset=utf-8'
    return response
```

### 2. Blueprint-Specific Handlers

Updated existing `after_request` handlers in blueprints to include UTF-8 charset setting:

#### Analysis Routes (`backend/app/blueprints/analyse/routes.py`)
```python
# Set UTF-8 charset for JSON responses to properly handle Korean characters
if response.content_type and response.content_type.startswith('application/json'):
    response.content_type = 'application/json; charset=utf-8'
```

#### Upload Routes (`backend/app/blueprints/upload/views.py`)
```python
# Set UTF-8 charset for JSON responses to properly handle Korean characters
if response.content_type and response.content_type.startswith('application/json'):
    response.content_type = 'application/json; charset=utf-8'
```

#### Data Process Routes (`backend/app/blueprints/data_process/routes.py`)
```python
# Set UTF-8 charset for JSON responses to properly handle Korean characters
if response.content_type and response.content_type.startswith('application/json'):
    response.content_type = 'application/json; charset=utf-8'
```

#### API Routes (`backend/app/blueprints/api/routes.py`)
```python
# Set UTF-8 charset for JSON responses to properly handle Korean characters
if response.content_type and response.content_type.startswith('application/json'):
    response.content_type = 'application/json; charset=utf-8'
```

## How It Works

1. **Response Interception**: The `after_request` handlers intercept all HTTP responses before they're sent to the client
2. **Content-Type Check**: Check if the response is JSON by examining the `Content-Type` header
3. **Charset Addition**: If it's a JSON response, modify the `Content-Type` header to include `charset=utf-8`
4. **Header Result**: Changes `application/json` to `application/json; charset=utf-8`

## Benefits

1. **Automatic Application**: All JSON responses automatically get the UTF-8 charset
2. **No Code Changes**: Existing endpoints don't need modification
3. **Browser Compatibility**: Ensures proper character encoding interpretation across all browsers
4. **Korean Character Support**: Fixes corruption of Korean text in JSON responses
5. **Future-Proof**: New endpoints automatically inherit the UTF-8 charset setting

## Testing

Use the provided test script to verify the implementation:

```bash
python test_utf8_charset.py
```

The test script checks:
- That JSON responses include `charset=utf-8` in the `Content-Type` header
- That Korean characters are properly handled
- That various endpoints return the correct headers

## Expected Results

### Before Implementation
```
Content-Type: application/json
```

### After Implementation
```
Content-Type: application/json; charset=utf-8
```

## Affected Endpoints

All endpoints that return JSON responses now include the UTF-8 charset:

- **Analysis endpoints**: `/api/analyse/*`, `/api/bert/*`, `/api/sentrans/*`, etc.
- **Data processing**: `/api/language-groups`, `/api/pos-tags/*`, etc.
- **File operations**: Upload, download, session management
- **API endpoints**: Session status, file serving, etc.

## Browser Impact

With this implementation:
- Korean characters display correctly in all modern browsers
- No more character corruption in JSON responses
- Consistent encoding across different browser types and versions
- Proper handling of mixed Korean/English content

## Maintenance

The implementation is self-maintaining:
- New endpoints automatically inherit UTF-8 charset setting
- No additional configuration required
- Works with existing CORS and other response headers
- Compatible with all Flask response types

## Troubleshooting

If Korean characters still appear corrupted:

1. **Check Response Headers**: Verify that `Content-Type: application/json; charset=utf-8` is present
2. **Browser Cache**: Clear browser cache and reload
3. **Network Tools**: Use browser developer tools to inspect response headers
4. **Test Script**: Run the provided test script to verify implementation

## Technical Notes

- The implementation preserves existing CORS headers and other response modifications
- UTF-8 charset is only added to JSON responses, not affecting other content types
- The solution is compatible with Flask's built-in `jsonify()` function
- No performance impact as the header modification is minimal
