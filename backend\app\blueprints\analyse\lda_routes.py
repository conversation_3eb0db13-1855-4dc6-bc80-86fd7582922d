import os

import matplotlib
from flask import current_app, jsonify, request, url_for

from ...services.json_session_service import get_session_value
from ...services.nlp_service import (create_topic_network,
                                     create_updated_pyldavis, get_file_path,
                                     get_lda_model, get_updated_topics,
                                     run_lda_analysis,
                                     run_lda_analysis_with_edits,
                                     update_topic_visualizations)
from ...services.session_service import get_or_create_session_id
from . import lda_bp

matplotlib.use("Agg")
import matplotlib.pyplot as plt

# Global variables for LDA model state
global_model = None
global_corpus = None
global_dictionary = None
global_tokens = None
global_edited_keywords = {}

# Configure CORS for React frontend with HTTPS support
@lda_bp.after_request
def after_request(response):
    # Get CORS allowed origins from environment variable or use a default
    cors_allowed_origins = os.getenv("CORS_ALLOWED_ORIGINS", "*")

    # If specific origins are provided, use them
    if cors_allowed_origins != "*":
        # Check if the request origin is in the allowed origins
        request_origin = request.headers.get("Origin")
        if request_origin:
            if "," in cors_allowed_origins:
                allowed_origins = cors_allowed_origins.split(",")
                if request_origin in allowed_origins:
                    response.headers.add("Access-Control-Allow-Origin", request_origin)
            else:
                if request_origin == cors_allowed_origins:
                    response.headers.add("Access-Control-Allow-Origin", request_origin)
    else:
        # Wildcard for development
        response.headers.add("Access-Control-Allow-Origin", "*")

    response.headers.add(
        "Access-Control-Allow-Headers", "Content-Type,Authorization,X-Requested-With"
    )
    response.headers.add("Access-Control-Allow-Methods", "GET,PUT,POST,DELETE,OPTIONS")
    response.headers.add("Access-Control-Allow-Credentials", "true")

    # Add Vary header to ensure proper caching with CORS
    response.headers.add("Vary", "Origin")

    # Set UTF-8 charset for JSON responses to properly handle Korean characters
    if response.content_type and response.content_type.startswith('application/json'):
        response.content_type = 'application/json; charset=utf-8'

    return response

# (4-3) 데이터 처리 및 LDA 분석 수행
@lda_bp.route("/process", methods=["POST", "OPTIONS"])
def process_data():
    """LDA 분석 처리"""
    if request.method == "OPTIONS":
        return "", 200

    # (4-3-1) 업로드된 파일 존재 확인
    session_id = request.args.get("session_id")
    if not session_id:
        session_id = get_or_create_session_id()

    filename = get_session_value(session_id, "uploaded_file")
    if not filename:
        return jsonify({"error": "업로드된 파일이 없습니다"})

    # (4-3-2) 분석 파라미터 가져오기
    data = request.get_json()
    text_column = data.get("text_column")  # 텍스트 데이터가 있는 컬럼명
    min_topic = int(data.get("min_topic", 3))  # 최소 토픽 개수
    max_topic = int(data.get("max_topic", 10))  # 최대 토픽 개수
    no_below = int(data.get("no_below", 5))  # 최소 문서 빈도
    no_above = float(data.get("no_above", 0.2))  # 최대 문서 빈도

    # (4-3-3) 네트워크 시각화 스타일 설정
    network_style = data.get(
        "network_style", "academic"
    )  # 기본값을 논문용 스타일로 변경

    # 차트 스타일 설정 추가
    chart_style = data.get("chart_style", "default")  # 기본값은 default 스타일

    # (4-3-4) 수동 토픽 수 설정 확인
    manual_topic_number = data.get("manual_topic_number")

    # (4-3-5) 파일 경로 생성 및 존재 확인
    file_path = get_file_path(session_id, filename)

    # 파일 존재 확인
    if not os.path.exists(file_path):
        return jsonify({"error": "파일을 찾을 수 없습니다."}), 404

    # (4-3-6) 편집된 키워드 초기화 (새로운 모델 학습 시)
    global global_edited_keywords
    global_edited_keywords = {}

    # (4-3-7) LDA 분석 실행 - 전체 모드가 기본값
    # 빠른 모드 설정 (기본값: False - 전체 모드)
    fast_mode = data.get("fast_mode", False)

    try:
        results = run_lda_analysis(
            file_path,
            text_column,
            min_topic,
            max_topic,
            no_below,
            no_above,
            network_style,
            manual_topic_number,
            chart_style,  # 차트 스타일 매개변수 추가
            fast_mode,  # 빠른 모드 매개변수 추가
        )

        # Add full download URLs to the results
        if "csv_path" in results:
            results["csv_download_url"] = url_for(
                "process.serve_file",
                filepath=results["csv_path"],
                download="true",
                session_id=session_id,
                _external=True,
            )

        if "network_img_path" in results:
            results["network_img_url"] = url_for(
                "process.serve_file",
                filepath=results["network_img_path"],
                download="false",
                session_id=session_id,
                _external=True,
            )

        # Add URLs for topic images
        if "topic_images" in results and isinstance(results["topic_images"], list):
            for topic_image in results["topic_images"]:
                if "path" in topic_image:
                    topic_image["url"] = url_for(
                        "process.serve_file",
                        filepath=topic_image["path"],
                        download="false",
                        session_id=session_id,
                        _external=True,
                    )

        return jsonify(results)
    except Exception as e:
        return jsonify({"error": str(e)})


# (4-4) 키워드 편집 처리
@lda_bp.route("/edit_keywords", methods=["POST", "OPTIONS"])
def edit_keywords():
    """키워드 편집 처리"""
    if request.method == "OPTIONS":
        return "", 200
    global global_model, global_corpus, global_dictionary, global_tokens, global_edited_keywords

    # (4-4-1) 모델 로드 확인 - get_lda_model 함수 사용
    session_id = request.args.get("session_id")
    global_model, global_corpus, global_dictionary = get_lda_model(session_id)
    # print(global_model, "GLOBAL MODEL")
    if global_model is None:
        return jsonify(
            {"error": "모델이 로드되지 않았습니다. 먼저 데이터를 처리해주세요."}
        )

    # (4-4-2) 편집 정보 가져오기
    data = request.get_json()
    topic_id = int(data.get("topic_id"))
    edited_words = data.get("edited_words", []) # pass as list of dictionaries
    removed_words = data.get("removed_words", []) # list of strings
    chart_style = data.get("chart_style", "default")

    # (4-4-3) 편집된 키워드 정보 저장 구조 초기화
    if topic_id not in global_edited_keywords:
        global_edited_keywords[topic_id] = {"edited": {}, "removed": []}

    # (4-4-4) 편집된 단어 정보 업데이트
    for word_info in edited_words:
        original_word = word_info.get("original")
        new_word = word_info.get("new")
        if original_word and new_word and original_word.lower() != new_word.lower():
            global_edited_keywords[topic_id]["edited"][
                original_word.lower()
            ] = new_word.lower()

    # (4-4-5) 제거된 단어 정보 업데이트 - 수정된 로직
    for word in removed_words:
        word_lower = word.lower()
        # 제거 목록에 추가
        if word_lower not in global_edited_keywords[topic_id]["removed"]:
            global_edited_keywords[topic_id]["removed"].append(word_lower)

        # 만약 이 단어가 이전에 편집되었다면, 편집 정보도 제거
        # 편집된 단어들 중에서 이 단어와 일치하는 것을 찾아서 제거
        edited_dict = global_edited_keywords[topic_id]["edited"]
        keys_to_remove = []
        for original_key, edited_value in edited_dict.items():
            # 원본 단어나 편집된 단어가 제거 대상이면 편집 정보 삭제
            if original_key == word_lower or edited_value == word_lower:
                keys_to_remove.append(original_key)

        for key in keys_to_remove:
            del edited_dict[key]

    # (4-4-6) Call run_lda_analysis with skip_training=True to get the same structure as process endpoint
    try:
        # Get the file path for reanalysis (needed for some visualization functions)
        filename = get_session_value(session_id, "uploaded_file")
        if not filename:
            return jsonify({"error": "업로드된 파일이 없습니다"})

        file_path = get_file_path(session_id, filename)
        if not os.path.exists(file_path):
            return jsonify({"error": "파일을 찾을 수 없습니다."}), 404

        # Call run_lda_analysis but skip training and use existing model with edited keywords
        result = run_lda_analysis_with_edits(
            file_path=file_path,
            existing_model=global_model,
            existing_corpus=global_corpus,
            existing_dictionary=global_dictionary,
            edited_keywords=global_edited_keywords,
            network_style=data.get("network_style", "academic"),
            chart_style=chart_style,
            session_id=session_id,
        )

        # Add download URLs (these are already included in the result from run_lda_analysis_with_edits)
        if "csv_path" in result:
            result["csv_download_url"] = url_for(
                "process.serve_file",
                filepath=result["csv_path"],
                download="true",
                session_id=session_id,
                _external=True,
            )

        if "network_img_path" in result:
            result["network_img_url"] = url_for(
                "process.serve_file",
                filepath=result["network_img_path"],
                download="false",
                session_id=session_id,
                _external=True,
            )

        # Add URLs for topic images
        if "topic_images" in result and isinstance(result["topic_images"], list):
            for topic_image in result["topic_images"]:
                if "path" in topic_image:
                    topic_image["url"] = url_for(
                        "process.serve_file",
                        filepath=topic_image["path"],
                        download="false",
                        session_id=session_id,
                        _external=True,
                    )

        # (4-4-7) 편집 결과 반환
        return jsonify(result)
    except Exception as e:
        return jsonify({"error": str(e)})


# 차트 스타일 변경 엔드포인트 추가
@lda_bp.route("/update_chart_style", methods=["POST", "OPTIONS"])
def update_chart_style():
    """차트 스타일 변경"""
    if request.method == "OPTIONS":
        return "", 200
    global global_model, global_edited_keywords

    # 새로운 get_lda_model 함수를 사용하여 모델 가져오기
    session_id = request.args.get("session_id")
    global_model, _, _ = get_lda_model(session_id)

    # 모델이 없으면 오류 반환
    if global_model is None:
        return jsonify(
            {"error": "모델이 로드되지 않았습니다. 먼저 데이터를 처리해주세요."}
        )

    data = request.get_json()
    chart_style = data.get("chart_style", "default")

    try:
        # 토픽별 시각화 이미지 업데이트
        topic_images = update_topic_visualizations(
            global_model, global_edited_keywords, chart_style
        )

        # Get session ID for URLs
        session_id = request.args.get("session_id")
        if not session_id:
            session_id = get_or_create_session_id()

        # Add URLs for topic images
        if topic_images and isinstance(topic_images, list):
            for topic_image in topic_images:
                if "path" in topic_image:
                    topic_image["url"] = url_for(
                        "process.serve_file",
                        filepath=topic_image["path"],
                        download="false",
                        session_id=session_id,
                        _external=True,
                    )

        return jsonify(
            {"success": True, "topic_images": topic_images, "chart_style": chart_style}
        )
    except Exception as e:
        # 오류 발생 시 기본 이미지 생성
        result_folder = current_app.config["RESULT_FOLDER"]
        session_id = request.args.get("session_id")
        if not session_id:
            session_id = get_or_create_session_id()
        session_folder = os.path.join(result_folder, session_id)
        topic_folder = os.path.join(
            session_folder, current_app.config["TOPIC_MODELS_FOLDER"]
        )

        try:
            used_topic_num = global_model.num_topics if global_model else 3
            topic_images = []

            for topic_id in range(used_topic_num):
                topic_img_path = os.path.join(
                    topic_folder, f"topic_{topic_id + 1}_default.png"
                )

                # 기본 이미지 파일이 없는 경우 생성
                if not os.path.exists(topic_img_path):
                    try:
                        # 이미지 저장을 위한 디렉토리 확인
                        os.makedirs(os.path.dirname(topic_img_path), exist_ok=True)

                        # 매우 간단한 막대 그래프 생성
                        plt.figure(figsize=(10, 6))
                        plt.barh(
                            [f"단어 {i + 1}" for i in range(5)],
                            [5 - i for i in range(5)],
                        )
                        plt.title(f"TOPIC {topic_id + 1} (기본 이미지)")
                        plt.xlabel("가중치")
                        plt.ylabel("키워드")
                        plt.savefig(topic_img_path)
                        plt.close()
                    except Exception as e2:
                        print(f"기본 토픽 이미지 생성 실패: {str(e2)}")

                topic_images.append({"id": topic_id, "path": topic_img_path})

            # Add URLs for topic images
            if topic_images and isinstance(topic_images, list):
                for topic_image in topic_images:
                    if "path" in topic_image:
                        topic_image["url"] = url_for(
                            "process.serve_file",
                            filepath=topic_image["path"],
                            download="false",
                            session_id=session_id,
                            _external=True,
                        )

            return jsonify(
                {
                    "success": True,
                    "topic_images": topic_images,
                    "chart_style": chart_style,
                    "warning": "차트 스타일 변경 중 오류가 발생하여 기본 이미지가 생성되었습니다.",
                }
            )
        except Exception as recovery_error:
            return jsonify(
                {
                    "error": f"차트 스타일 변경 및 복구 시도 실패: {str(e)}, 복구 오류: {str(recovery_error)}"
                }
            )


@lda_bp.route("/update_topics", methods=["POST", "OPTIONS"])
def update_topics():
    """토픽 업데이트"""
    if request.method == "OPTIONS":
        return "", 200
    # Get current session ID
    session_id = request.args.get("session_id")
    if not session_id:
        session_id = get_or_create_session_id()

    # Get edited keywords from request
    data = request.get_json()
    edited_keywords = data.get("edited_keywords", {})

    # Get current model
    global global_model, global_corpus, global_dictionary
    global_model, global_corpus, global_dictionary = get_lda_model()

    if global_model is None:
        return jsonify({"error": "No LDA model found"})

    try:
        # Update topics with edited keywords
        topics = get_updated_topics(global_model, edited_keywords)

        # Create updated visualizations
        vis_html = create_updated_pyldavis(
            global_model, global_corpus, global_dictionary, edited_keywords
        )
        topic_images = update_topic_visualizations(global_model, edited_keywords)

        return jsonify(
            {"topics": topics, "pyldavis_html": vis_html, "topic_images": topic_images}
        )
    except Exception as e:
        return jsonify({"error": str(e)})


@lda_bp.route("/update_network", methods=["POST", "OPTIONS"])
def update_network():
    """네트워크 업데이트"""
    if request.method == "OPTIONS":
        return "", 200
    # Get current session ID
    session_id = request.args.get("session_id")
    if not session_id:
        session_id = get_or_create_session_id()

    # Get edited keywords and style from request
    data = request.get_json()
    edited_keywords = data.get("edited_keywords", {})
    network_style = data.get("network_style", "academic")

    # Get current model
    global global_model
    global_model, _, _ = (
        get_lda_model()
    )  # We only need the model for network visualization

    if global_model is None:
        return jsonify({"error": "No LDA model found"})

    try:
        # Create updated network visualization
        network_img_path = create_topic_network(
            global_model,
            global_model.num_topics,
            network_style=network_style,
            edited_keywords=edited_keywords,
        )

        # Add download URL to the response
        network_img_url = url_for(
            "process.serve_file",
            filepath=network_img_path,
            download="false",
            session_id=session_id,
            _external=True,
        )
        
        return jsonify(
            {"network_img_path": network_img_path, "network_img_url": network_img_url}
        )
    except Exception as e:
        return jsonify({"error": str(e)})
